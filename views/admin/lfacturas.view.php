<?php
#region region DOCS

/** @var array $facturas */
/** @var array $proveedores */
/** @var bool $showResults */
/** @var array $filtros */
/** @var string $error_display */
/** @var string $error_text */
/** @var string $success_display */
/** @var string $success_text */

use App\classes\Factura;
use App\classes\Proveedor;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Listado de Facturas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="Gestión de facturas del sistema" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
    <link href="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- BEGIN #header -->
    <?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
    <!-- END #header -->

    <!-- BEGIN #sidebar -->
    <?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
    <!-- END #sidebar -->

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Listado de Facturas</h4>
                <p class="mb-0 text-muted">Gestiona las facturas del sistema con filtros avanzados</p>
            </div>
            <div class="ms-auto">
                <a href="ingresar-factura" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nueva</a>
            </div>
        </div>
        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region region FLASH MESSAGES ?>
        <?php if ($success_display === 'show'): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" id="success-alert">
                <?php echo $success_text; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php if ($error_display === 'show'): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" id="error-alert">
                <?php echo $error_text; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php #endregion FLASH MESSAGES ?>

        <?php #region region FILTERS PANEL ?>
        <div class="panel panel-inverse no-border-radious mb-3">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">Filtros de Búsqueda</h4>
                <div class="panel-heading-btn">
                    <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                    <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                </div>
            </div>
            <div class="panel-body">
                <form method="POST" action="listado-facturas" id="filtros-form">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="id_factura" class="form-label"># Factura</label>
                            <input type="number" class="form-control" id="id_factura" name="id_factura" 
                                   value="<?php echo htmlspecialchars($filtros['id_factura']); ?>" 
                                   placeholder="Ej: 123">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="id_proveedor" class="form-label">Proveedor</label>
                            <select class="form-select" id="id_proveedor" name="id_proveedor">
                                <option value="">Todos los proveedores</option>
                                <?php foreach ($proveedores as $proveedor): ?>
                                    <option value="<?php echo $proveedor->getId(); ?>" 
                                            <?php echo ($filtros['id_proveedor'] == $proveedor->getId()) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($proveedor->getNombre()); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="fecha_desde" class="form-label">Fecha Desde</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_desde" name="fecha_desde" 
                                       value="<?php echo htmlspecialchars($filtros['fecha_desde']); ?>" 
                                       autocomplete="off" placeholder="yyyy-mm-dd">
                                <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="fecha_hasta" class="form-label">Fecha Hasta</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_hasta" name="fecha_hasta" 
                                       value="<?php echo htmlspecialchars($filtros['fecha_hasta']); ?>" 
                                       autocomplete="off" placeholder="yyyy-mm-dd">
                                <span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" name="aplicar_filtros" class="btn btn-primary">
                            <i class="fa fa-search fa-fw me-1"></i> Aplicar Filtros
                        </button>
                        <a href="listado-facturas" class="btn btn-secondary">
                            <i class="fa fa-times fa-fw me-1"></i> Limpiar
                        </a>
                    </div>
                </form>
            </div>
        </div>
        <?php #endregion FILTERS PANEL ?>

        <?php #region region RESULTS PANEL ?>
        <?php if ($showResults): ?>
        <div class="panel panel-inverse no-border-radious">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">
                    Resultados de la Búsqueda (<?php echo count($facturas); ?> facturas encontradas)
                </h4>
            </div>
            <div class="p-1 table-nowrap" style="overflow: auto">
                <?php if (empty($facturas)): ?>
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle fa-2x mb-2"></i>
                        <p class="mb-0">No se encontraron facturas que coincidan con los filtros aplicados.</p>
                    </div>
                <?php else: ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th>Acciones</th>
                            <th>#</th>
                            <th>Proveedor</th>
                            <th class="text-center">Fecha</th>
                            <th class="text-end">Valor Total</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px" id="facturas-table-body">
                        <?php foreach ($facturas as $factura): ?>
                            <?php $proveedor = $factura->getProveedor($conexion); ?>
                            <tr data-factura-id="<?php echo $factura->getId(); ?>">
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-xs btn-info btn-view-detalles"
                                                title="Ver Detalles"
                                                data-factura-id="<?php echo $factura->getId(); ?>">
                                            <i class="fa fa-list"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-warning btn-view-soportes"
                                                title="Ver Soportes"
                                                data-factura-id="<?php echo $factura->getId(); ?>">
                                            <i class="fa fa-file-pdf"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-danger btn-soft-delete"
                                                title="Eliminar"
                                                data-factura-id="<?php echo $factura->getId(); ?>"
                                                data-proveedor="<?php echo htmlspecialchars($proveedor ? $proveedor->getNombre() : 'N/A'); ?>">
                                            <i class="fa fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                                <td><?php echo $factura->getId(); ?></td>
                                <td><?php echo htmlspecialchars($proveedor ? $proveedor->getNombre() : 'N/A'); ?></td>
                                <td class="text-center"><?php echo htmlspecialchars($factura->getFecha()); ?></td>
                                <td class="text-end">
                                    $<?php echo number_format($factura->getValor_total() ?? 0, 0, ',', '.'); ?> COP
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
        <?php else: ?>
        <div class="panel panel-inverse no-border-radious">
            <div class="panel-body text-center">
                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Aplique filtros para ver las facturas</h5>
                <p class="text-muted">Use los filtros de arriba para buscar facturas específicas.</p>
            </div>
        </div>
        <?php endif; ?>
        <?php #endregion RESULTS PANEL ?>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region MODALS ?>



<!-- View Details Modal -->
<div class="modal fade" id="viewDetallesModal" tabindex="-1" aria-labelledby="viewDetallesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title" id="viewDetallesModalLabel">
                    <i class="fa fa-file-invoice me-2"></i>Resumen de la Factura
                </h4>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div id="detalles-content">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-3 text-muted">Cargando detalles de la factura...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fa fa-times me-1"></i>Cerrar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Manage Support Documents Modal -->
<div class="modal fade" id="viewSoportesModal" tabindex="-1" aria-labelledby="viewSoportesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSoportesModalLabel">Gestionar Documentos de Soporte</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Add new support document section -->
                <div class="card mb-3">
                    <div class="card-body">
                        <form id="add-soporte-form" enctype="multipart/form-data">
                            <input type="hidden" id="soporte-factura-id" name="factura_id">
                            <div class="row">
                                <div class="col-md-9">
                                    <label for="nuevo-archivo" class="form-label">Archivo PDF <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="file" class="form-control" id="nuevo-archivo" name="archivo" accept=".pdf" required>
                                        <button type="submit" class="btn btn-success w-100px" id="add-soporte-btn">
                                            <i class="fa fa-plus me-1"></i>Agregar
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <i class="fa fa-info-circle me-1"></i>
                                        Solo archivos PDF. Tamaño máximo: 10MB.
                                    </div>
                                    <div class="invalid-feedback" id="archivo-error">
                                        Debe seleccionar un archivo PDF válido.
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Existing support documents section -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fa fa-file-pdf me-2"></i>Documentos Existentes
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="soportes-content">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Cargando...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<?php #endregion MODALS ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA_RESOURCES ?>js/datepickerini.js"></script>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log("Facturas list page loaded.");
    const tableBody = document.getElementById('facturas-table-body');

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const successAlert = document.getElementById('success-alert');
        const errorAlert = document.getElementById('error-alert');

        if (successAlert && successAlert.classList.contains('show')) {
            const bsAlert = new bootstrap.Alert(successAlert);
            bsAlert.close();
        }

        if (errorAlert && errorAlert.classList.contains('show')) {
            const bsAlert = new bootstrap.Alert(errorAlert);
            bsAlert.close();
        }
    }, 5000);

    // Modal elements
    const viewDetallesModal = new bootstrap.Modal(document.getElementById('viewDetallesModal'));
    const viewSoportesModal = new bootstrap.Modal(document.getElementById('viewSoportesModal'));

    if (tableBody) {
        tableBody.addEventListener('click', function(event) {
            const viewDetallesBtn = event.target.closest('.btn-view-detalles');
            const viewSoportesBtn = event.target.closest('.btn-view-soportes');
            const softDeleteBtn = event.target.closest('.btn-soft-delete');

            // Handle View Details
            if (viewDetallesBtn) {
                event.preventDefault();
                const facturaId = viewDetallesBtn.dataset.facturaId;
                loadFacturaDetalles(facturaId);
                viewDetallesModal.show();
            }

            // Handle View Support Documents
            if (viewSoportesBtn) {
                event.preventDefault();
                const facturaId = viewSoportesBtn.dataset.facturaId;
                document.getElementById('soporte-factura-id').value = facturaId;
                loadFacturaSoportes(facturaId);
                viewSoportesModal.show();
            }

            // Handle Soft Delete
            if (softDeleteBtn) {
                event.preventDefault();
                const facturaId = softDeleteBtn.dataset.facturaId;
                const proveedor = softDeleteBtn.dataset.proveedor;

                swal({
                    title: '¿Está seguro?',
                    text: `¿Desea eliminar la factura del proveedor "${proveedor}"?`,
                    icon: 'warning',
                    buttons: {
                        cancel: {
                            text: "Cancelar",
                            value: null,
                            visible: true,
                            className: "btn-secondary",
                            closeModal: true,
                        },
                        confirm: {
                            text: "Sí, eliminar",
                            value: true,
                            visible: true,
                            className: "btn-danger",
                            closeModal: true
                        }
                    }
                }).then((willDelete) => {
                    if (willDelete) {
                        softDeleteFactura(facturaId);
                    }
                });
            }
        });
    }





    // Handle Add Support Document Form
    document.getElementById('add-soporte-form').addEventListener('submit', function(event) {
        event.preventDefault();

        const fileInput = document.getElementById('nuevo-archivo');
        const file = fileInput.files[0];

        // Validate file
        if (!file) {
            fileInput.classList.add('is-invalid');
            document.getElementById('archivo-error').textContent = 'Debe seleccionar un archivo.';
            return;
        }

        if (file.type !== 'application/pdf') {
            fileInput.classList.add('is-invalid');
            document.getElementById('archivo-error').textContent = 'El archivo debe ser un PDF.';
            return;
        }

        if (file.size > 10 * 1024 * 1024) {
            fileInput.classList.add('is-invalid');
            document.getElementById('archivo-error').textContent = 'El archivo es demasiado grande (máximo 10MB).';
            return;
        }

        // Clear validation
        fileInput.classList.remove('is-invalid');

        // Submit form
        const formData = new FormData(this);
        formData.append('action', 'add_soporte');

        const submitBtn = document.getElementById('add-soporte-btn');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Agregando...';
        submitBtn.disabled = true;

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reset form
                this.reset();

                // Reload support documents
                const facturaId = document.getElementById('soporte-factura-id').value;
                loadFacturaSoportes(facturaId);

                // Show success message
                showSweetAlertSuccess('Documento agregado', data.message);
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSweetAlertError('Error', 'Error de comunicación al agregar el documento.');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    // Load Factura Details
    function loadFacturaDetalles(facturaId) {
        const content = document.getElementById('detalles-content');
        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Cargando...</span></div></div>';

        const formData = new FormData();
        formData.append('action', 'get_detalles');
        formData.append('factura_id', facturaId);

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `
                    <!-- Invoice Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-body text-center">
                                    <div class="d-flex align-items-center justify-content-center mb-2">
                                        <i class="fa fa-hashtag text-primary me-2 fs-4"></i>
                                        <h5 class="card-title mb-0 text-primary">Factura</h5>
                                    </div>
                                    <h3 class="text-dark fw-bold">#${data.factura.id}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-info h-100">
                                <div class="card-body text-center">
                                    <div class="d-flex align-items-center justify-content-center mb-2">
                                        <i class="fa fa-building text-info me-2 fs-4"></i>
                                        <h5 class="card-title mb-0 text-info">Proveedor</h5>
                                    </div>
                                    <h6 class="text-dark fw-semibold fs-14px">${data.factura.proveedor_nombre}</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="card border-warning h-100">
                                <div class="card-body text-center">
                                    <div class="d-flex align-items-center justify-content-center mb-2">
                                        <i class="fa fa-calendar text-warning me-2 fs-4"></i>
                                        <h5 class="card-title mb-0 text-warning">Fecha</h5>
                                    </div>
                                    <h6 class="text-dark fw-semibold fs-14px">${data.factura.fecha}</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-success h-100">
                                <div class="card-body text-center">
                                    <div class="d-flex align-items-center justify-content-center mb-2">
                                        <i class="fa fa-dollar-sign text-success me-2 fs-4"></i>
                                        <h5 class="card-title mb-0 text-success">Valor Total</h5>
                                    </div>
                                    <h4 class="text-success fw-bold">$${new Intl.NumberFormat('es-CO').format(data.factura.valor_total || 0)} COP</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Details Section -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">
                                <i class="fa fa-list-alt text-primary me-2"></i>Detalles de la Factura
                            </h5>
                        </div>
                        <div class="card-body p-0">
                `;

                if (data.detalles.length > 0) {
                    html += `
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th class="border-0">
                                                <i class="fa fa-tag me-1"></i>Descripción
                                            </th>
                                            <th class="text-center border-0">
                                                <i class="fa fa-sort-numeric-up me-1"></i>Cantidad
                                            </th>
                                            <th class="text-end border-0">
                                                <i class="fa fa-dollar-sign me-1"></i>Valor Unitario
                                            </th>
                                            <th class="text-end border-0">
                                                <i class="fa fa-calculator me-1"></i>Valor Total
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;

                    data.detalles.forEach((detalle, index) => {                        
                        html += `
                            <tr class="table-dark">
                                <td class="fw-medium">${detalle.descripcion}</td>
                                <td class="text-center">
                                    <span class="badge bg-secondary">${detalle.cantidad}</span>
                                </td>
                                <td class="text-end fw-semibold text-primary">
                                    $${new Intl.NumberFormat('es-CO').format(detalle.valor_unitario || 0)}
                                </td>
                                <td class="text-end fw-bold text-success">
                                    $${new Intl.NumberFormat('es-CO').format(detalle.valor_total || 0)}
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    `;
                } else {
                    html += `
                            <div class="text-center py-5">
                                <i class="fa fa-inbox text-muted" style="font-size: 4rem;"></i>
                                <h5 class="text-muted mt-3">No hay detalles registrados</h5>
                                <p class="text-muted">Esta factura no tiene detalles asociados.</p>
                            </div>
                        </div>
                    </div>
                    `;
                }

                content.innerHTML = html;
            } else {
                content.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">Error al cargar los detalles de la factura.</div>';
        });
    }

    // Load Factura Support Documents
    function loadFacturaSoportes(facturaId) {
        const content = document.getElementById('soportes-content');
        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Cargando...</span></div></div>';

        const formData = new FormData();
        formData.append('action', 'get_soportes');
        formData.append('factura_id', facturaId);

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.soportes.length > 0) {
                    let html = '<div class="table-responsive">';
                    html += '<table class="table table-sm table-hover">';
                    html += '<thead><tr><th>Archivo</th><th>Tamaño</th><th class="text-center">Acciones</th></tr></thead>';
                    html += '<tbody>';

                    data.soportes.forEach(soporte => {
                        const fileSize = soporte.file_size ? `${(soporte.file_size / 1024 / 1024).toFixed(2)} MB` : 'N/A';
                        html += `
                            <tr>
                                <td>
                                    <i class="fa fa-file-pdf text-danger me-2"></i>
                                    ${soporte.nombre_archivo}
                                </td>
                                <td>${fileSize}</td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo RUTA_RESOURCES; ?>uploads/facturas/${facturaId}/${soporte.nombre_archivo}"
                                           target="_blank" class="btn btn-sm btn-outline-primary"
                                           title="Descargar">
                                            <i class="fa fa-download"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger btn-delete-soporte"
                                                data-soporte-id="${soporte.id}"
                                                data-archivo-nombre="${soporte.nombre_archivo}"
                                                title="Eliminar">
                                            <i class="fa fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    html += '</tbody></table></div>';

                    // Add event listeners for delete buttons
                    content.innerHTML = html;

                    // Attach delete event listeners
                    content.querySelectorAll('.btn-delete-soporte').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const soporteId = this.dataset.soporteId;
                            const archivoNombre = this.dataset.archivoNombre;
                            deleteSoporte(soporteId, archivoNombre, facturaId);
                        });
                    });
                } else {
                    content.innerHTML = '<div class="alert alert-info"><i class="fa fa-info-circle me-2"></i>No hay documentos de soporte para esta factura.</div>';
                }
            } else {
                content.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">Error al cargar los documentos de soporte.</div>';
        });
    }

    // Delete Support Document
    function deleteSoporte(soporteId, archivoNombre, facturaId) {
        swal({
            title: '¿Está seguro?',
            text: `¿Desea eliminar el documento "${archivoNombre}"?`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: "Cancelar",
                    value: null,
                    visible: true,
                    className: "btn-secondary",
                    closeModal: true,
                },
                confirm: {
                    text: "Sí, eliminar",
                    value: true,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                const formData = new FormData();
                formData.append('action', 'delete_soporte');
                formData.append('soporte_id', soporteId);
                formData.append('factura_id', facturaId);

                fetch('listado-facturas', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload support documents
                        loadFacturaSoportes(facturaId);

                        // Show success message
                        showSweetAlertSuccess('Documento eliminado', data.message);
                    } else {
                        showSweetAlertError('Error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showSweetAlertError('Error', 'Error de comunicación al eliminar el documento.');
                });
            }
        });
    }

    // Soft Delete Factura
    function softDeleteFactura(facturaId) {
        const formData = new FormData();
        formData.append('action', 'soft_delete');
        formData.append('factura_id', facturaId);

        fetch('listado-facturas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the row from the table
                const row = document.querySelector(`tr[data-factura-id="${facturaId}"]`);
                if (row) {
                    row.remove();
                }

                // Show success message
                showSweetAlertSuccess('Factura eliminada', data.message);

                // Check if table is now empty
                const tbody = document.getElementById('facturas-table-body');
                if (tbody && tbody.children.length === 0) {
                    location.reload(); // Reload to show "no results" message
                }
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSweetAlertError('Error', 'Error de comunicación al eliminar la factura.');
        });
    }
});
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
