<?php

require_once __ROOT__ . '/src/classes/marca.php';

class Producto
{
	public string $id;
	public string $nombre;
	public string $descripcion;
	public string $portada;
	public Marca  $marca;
	public float  $precio_venta;
	public int    $estado;
	
	function __construct()
	{
		$this->id           = '';
		$this->nombre       = '';
		$this->descripcion  = '';
		$this->portada      = '';
		$this->marca        = new Marca();
		$this->precio_venta = 0;
		$this->estado       = 0;
	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$objeto                = new self;
			$objeto->id            = $resultado['id'];
			$objeto->nombre        = $resultado['nombre'] ?? '';
			$objeto->descripcion   = $resultado['descripcion'] ?? '';
			$objeto->portada       = $resultado['portada'] ?? '';
			$objeto->marca->id     = $resultado['id_marca'] ?? '';
			$objeto->marca->nombre = $resultado['nombre_marca'] ?? '';
			$objeto->precio_venta  = $resultado['precio_venta'] ?? 0;
			$objeto->estado        = $resultado['estado'] ?? 0;
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, PDO $conexion): self
	{
		try {
			$query = <<<SQL
						SELECT * FROM productos p WHERE p.id = :id
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list(array $paramref, PDO $conexion): array
	{
		try {
			$query = <<<SQL
						SELECT
							 p.*
							,m.nombre AS 'nombre_marca'
						FROM productos p
						INNER JOIN marcas m ON p.id_marca = m.id
						WHERE
							p.estado = 1
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function agregar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			
			$query = <<<SQL
						INSERT INTO productos (
							 nombre
							,descripcion
							,id_marca
						) VALUES (
							 :nombre
							,:descripcion
							,:id_marca
						)
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":nombre", $this->nombre);
			$statement->bindValue(":descripcion", $this->descripcion);
			$statement->bindValue(":id_marca", $this->marca->id);
			$statement->execute();
			
			$this->id = $conexion->lastInsertId();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			
			$query = <<<SQL
						UPDATE productos SET
							 nombre = :nombre
							,descripcion = :descripcion
							,id_marca = :id_marca
							,precio_venta = :precio_venta
						WHERE
							id = :id
						SQL;
			
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":nombre", $this->nombre);
			$statement->bindValue(":descripcion", $this->descripcion);
			$statement->bindValue(":id_marca", $this->marca->id);
			$statement->bindValue(":precio_venta", $this->precio_venta);
			$statement->bindValue(":", $this->id);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function desactivar($id, PDO $conexion): void
	{
		try {
			$query = <<<SQL
						UPDATE productos SET estado = 0 WHERE id = :id
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function eliminar($id, PDO $conexion): void
	{
		try {
			$query = <<<SQL
						DELETE FROM productos WHERE id = :id
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data(): void
	{
		try {
			validar_campovacio($this->nombre, 'Debe especificar el nombre.');
			validar_campovacio($this->marca->id, 'Debe especificar la marca.');
			
			$this->nombre = mb_strtoupper($this->nombre);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>