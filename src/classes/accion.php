<?php

require_once __ROOT__ . '/src/classes/menuitem.php';
require_once __ROOT__ . '/src/classes/menusubitem.php';
require_once __ROOT__ . '/src/classes/perfil.php';

class Accion
{
	public string      $id;
	public string      $nombre;
	public string      $grupo;
	public MenuItem    $menu_item;
	public MenuSubItem $menu_sub_item;
	public int         $estado;
	public Perfil      $perfil;
	const ID_VER_DASHBOARD          = 1;
	const ID_CONSULTAR_MENU         = 2;
	const ID_CREAR_MENU             = 3;
	const ID_EDITAR_MENU            = 4;
	const ID_ELIMINAR_MENU          = 5;
	const ID_CONSULTAR_ACCION       = 6;
	const ID_CREAR_ACCION           = 7;
	const ID_EDITAR_ACCION          = 8;
	const ID_ELIMINAR_ACCION        = 9;
	const ID_CONSULTAR_PERFILES     = 10;
	const ID_CREAR_PERFILES         = 11;
	const ID_EDITAR_PERFILES        = 12;
	const ID_ELIMINAR_PERFILES      = 13;
	const ID_CONSULTAR_MARCAS       = 14;
	const ID_CREAR_MARCAS           = 15;
	const ID_EDITAR_MARCAS          = 16;
	const ID_ELIMINAR_MARCAS        = 17;
	const ID_CONSULTAR_PRODUCTOS    = 18;
	const ID_CREAR_PRODUCTOS        = 19;
	const ID_EDITAR_PRODUCTOS       = 20;
	const ID_ELIMINAR_PRODUCTOS     = 21;
	const ID_CONSULTAR_CLIENTES     = 22;
	const ID_CREAR_CLIENTES         = 23;
	const ID_EDITAR_CLIENTES        = 24;
	const ID_ELIMINAR_CLIENTES      = 25;
	const ID_CONSULTAR_AGENDA       = 26;
	const ID_CREAR_AGENDA           = 27;
	const ID_EDITAR_AGENDA          = 28;
	const ID_ELIMINAR_AGENDA        = 29;
	const ID_CONSULTAR_SERVICIOS    = 30;
	const ID_CREAR_SERVICIOS        = 31;
	const ID_EDITAR_SERVICIOS       = 32;
	const ID_ELIMINAR_SERVICIOS     = 33;
	const ID_CONSULTAR_COTIZACIONES = 34;
	const ID_CREAR_COTIZACIONES     = 35;
	const ID_EDITAR_COTIZACIONES    = 36;
	const ID_ELIMINAR_COTIZACIONES  = 37;
	
	function __construct()
	{
		$this->id            = '';
		$this->nombre        = '';
		$this->grupo         = '';
		$this->menu_item     = new MenuItem();
		$this->menu_sub_item = new MenuSubItem();
		$this->perfil        = new Perfil();
		$this->estado        = 0;
	}
	
	/**
	 * @param $resultado
	 *
	 * @return Accion
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$accion                       = new Accion;
			$accion->id                   = $resultado['id'];
			$accion->nombre               = $resultado['nombre'];
			$accion->grupo                = $resultado['grupo'];
			$accion->menu_item->id        = $resultado['id_menu_item'];
			$accion->menu_item->texto     = $resultado['texto_menu_item'] ?? '';
			$accion->menu_sub_item->id    = $resultado['id_menu_sub_item'];
			$accion->menu_sub_item->texto = $resultado['texto_menu_sub_item'] ?? '';
			$accion->estado               = $resultado['estado'];
			$accion->perfil->id           = $resultado['id_perfil'] ?? '';
			
			
			return $accion;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, $conexion): self
	{
		try {
			$query = <<<SQL
						SELECT * FROM acciones a WHERE a.id = :id
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list($conexion): array
	{
		try {
			$query = <<<SQL
						SELECT
							a.*
							,IFNULL(mi.texto, '') AS texto_menu_item
							,IFNULL(ms.texto, '') AS texto_menu_sub_item
						FROM acciones a
						LEFT JOIN menu_items mi ON a.id_menu_item = mi.id
						LEFT JOIN menu_subitems ms ON a.id_menu_sub_item = ms.id
						WHERE
							a.estado = 1
						ORDER BY
							 a.grupo
							,a.nombre
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$actions = array();
				
				foreach ($resultados as $resultado) {
					$actions[] = self::construct($resultado);
				}
				
				return $actions;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_porperfil($id_perfil, PDO $conexion): array
	{
		try {
			$query = <<<SQL
						SELECT
							a.*
							,IFNULL(pa.id_perfil, 0) AS id_perfil
						FROM acciones a
						LEFT JOIN perfiles_acciones pa ON pa.id_accion = a.id AND pa.id_perfil = :id_perfil
						ORDER BY
							 a.grupo
							,a.nombre
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_perfil", $id_perfil);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$acciones = array();
				
				foreach ($resultados as $resultado) {
					$acciones[] = Accion::construct($resultado);
				}
				
				return $acciones;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_grupo(PDO $conexion): array
	{
		try {
			$query = <<<SQL
					SELECT
						a.grupo
					FROM acciones a
					WHERE
						a.estado = 1
					GROUP BY
						a.grupo
					ORDER BY
						a.grupo
					SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$grupos = array();
				
				foreach ($resultados as $resultado) {
					$grupos[] = $resultado['grupo'];
				}
				
				return $grupos;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function agregar($conexion): void
	{
		try {
			$query = <<<SQL
						INSERT INTO acciones (
							 nombre
							,grupo
							,id_menu_item
							,id_menu_sub_item
						) VALUES (
							 :nombre
							,:grupo
							,:id_menu_item
							,:id_menu_sub_item
						)
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":nombre", strtoupper($this->nombre));
			$statement->bindValue(":grupo", strtoupper($this->grupo));
			$statement->bindValue(":id_menu_item", $this->menu_item->id);
			$statement->bindValue(":id_menu_sub_item", $this->menu_sub_item->id);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar($conexion): void
	{
		try {
			$query = <<<SQL
						UPDATE acciones SET
							 nombre = :nombre
							,grupo = :grupo
							,id_menu_item = :id_menu_item
							,id_menu_sub_item = :id_menu_sub_item
						WHERE
							id = :id
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":nombre", strtoupper($this->nombre));
			$statement->bindValue(":grupo", strtoupper($this->grupo));
			$statement->bindValue(":id_menu_item", $this->menu_item->id);
			$statement->bindValue(":id_menu_sub_item", $this->menu_sub_item->id);
			$statement->bindValue(":id", $this->id);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>