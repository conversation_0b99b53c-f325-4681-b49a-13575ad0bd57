<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Sprint
{
    // --- Atributos ---
    private ?int    $id                 = null;
    private ?int    $id_proyecto        = null;
    private ?string $descripcion        = null;
    private ?string $fecha_inicio       = null;
    private ?string $fecha_fin          = null;
    private ?int    $terminado          = null;
    private ?string $cambios_bd         = null;
    private ?string $cambios_resources  = null;
    private ?int    $estado             = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Sprint.
     */
    public function __construct()
    {
        $this->id                = 0;
        $this->descripcion       = null;
        $this->fecha_inicio      = null;
        $this->fecha_fin         = null;
        $this->terminado         = 0; // No terminado por defecto
        $this->cambios_bd        = null;
        $this->cambios_resources = null;
        $this->estado            = 1; // Estado activo por defecto
    }

    /**
     * Método estático para construir un objeto Sprint desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del sprint.
     *
     * @return self Instancia de Sprint.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                     = new self();
            $objeto->id                 = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->descripcion        = $resultado['descripcion'] ?? null;
            $objeto->fecha_inicio       = $resultado['fecha_inicio'] ?? null;
            $objeto->fecha_fin          = $resultado['fecha_fin'] ?? null;
            $objeto->terminado          = isset($resultado['terminado']) ? (int)$resultado['terminado'] : 0;
            $objeto->cambios_bd         = $resultado['cambios_bd'] ?? null;
            $objeto->cambios_resources  = $resultado['cambios_resources'] ?? null;
            $objeto->estado             = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir objeto Sprint: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un sprint por su ID.
     *
     * @param int $id       ID del sprint.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Sprint o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_id(int $id, PDO $conexion): ?self
    {
        try {
            // Consulta para obtener sprint por ID
            $query = <<<SQL
            SELECT
            	*
            FROM sprints
            WHERE
            	id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Sprint por ID: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de sprints activos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Sprint.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            // Consulta para obtener lista de sprints activos
            $query = <<<SQL
            SELECT
            	*
            FROM sprints
            WHERE
            	estado = 1
            ORDER BY
            	fecha_inicio DESC, id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Sprints: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo sprint en la base de datos a partir de un objeto Sprint.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo sprint creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if (empty($this->getDescripcion())) {
            throw new Exception("La descripción es requerida para crear el sprint.");
        }

        try {
            // Preparar la consulta INSERT
            $query = <<<SQL
            INSERT INTO sprints (
            	 descripcion
            	,fecha_inicio
            	,fecha_fin
            	,terminado
            	,cambios_bd
            	,cambios_resources
            	,estado
            ) VALUES (
            	 :descripcion
            	,:fecha_inicio
            	,:fecha_fin
            	,:terminado
            	,:cambios_bd
            	,:cambios_resources
            	,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_fin', $this->getFecha_fin(), PDO::PARAM_STR);
            $statement->bindValue(':terminado', $this->getTerminado(), PDO::PARAM_INT);
            $statement->bindValue(':cambios_bd', $this->getCambios_bd(), PDO::PARAM_STR);
            $statement->bindValue(':cambios_resources', $this->getCambios_resources(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID del sprint recién creado
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear sprint: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear sprint: " . $e->getMessage());
        }
    }

    /**
     * Actualiza un sprint existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    function actualizar(PDO $conexion): bool
    {
        // Validaciones básicas sobre el objeto
        if (empty($this->getDescripcion()) || $this->getId() === null || $this->getId() <= 0) {
            throw new Exception("La descripción y un ID válido son requeridos para actualizar el sprint.");
        }

        try {
            // Preparar la consulta UPDATE
            $query = <<<SQL
            UPDATE sprints SET
            	 descripcion = :descripcion
            	,fecha_inicio = :fecha_inicio
            	,fecha_fin = :fecha_fin
            	,terminado = :terminado
            	,cambios_bd = :cambios_bd
            	,cambios_resources = :cambios_resources
            	,estado = :estado
            WHERE
            	id = :id
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_fin', $this->getFecha_fin(), PDO::PARAM_STR);
            $statement->bindValue(':terminado', $this->getTerminado(), PDO::PARAM_INT);
            $statement->bindValue(':cambios_bd', $this->getCambios_bd(), PDO::PARAM_STR);
            $statement->bindValue(':cambios_resources', $this->getCambios_resources(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar sprint (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Desactiva un sprint estableciendo su estado a 0.
     *
     * @param int $id       ID del sprint a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            // Consulta para actualizar el estado a 0 (inactivo)
            $query = <<<SQL
            UPDATE sprints SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar sprint (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Elimina un sprint de la base de datos (eliminación física).
     *
     * @param int $id       ID del sprint a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM sprints
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar sprint (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getDescripcion(): ?string
    {
        return $this->descripcion;
    }

    public function setDescripcion(?string $descripcion): self
    {
        $this->descripcion = $descripcion;
        return $this;
    }

    public function getFecha_inicio(): ?string
    {
        return $this->fecha_inicio;
    }

    public function setFecha_inicio(?string $fecha_inicio): self
    {
        $this->fecha_inicio = $fecha_inicio;
        return $this;
    }

    public function getFecha_fin(): ?string
    {
        return $this->fecha_fin;
    }

    public function setFecha_fin(?string $fecha_fin): self
    {
        $this->fecha_fin = $fecha_fin;
        return $this;
    }

    public function getTerminado(): ?int
    {
        return $this->terminado;
    }

    public function setTerminado(?int $terminado): self
    {
        $this->terminado = $terminado;
        return $this;
    }

    public function getCambios_bd(): ?string
    {
        return $this->cambios_bd;
    }

    public function setCambios_bd(?string $cambios_bd): self
    {
        $this->cambios_bd = $cambios_bd;
        return $this;
    }

    public function getCambios_resources(): ?string
    {
        return $this->cambios_resources;
    }

    public function setCambios_resources(?string $cambios_resources): self
    {
        $this->cambios_resources = $cambios_resources;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el sprint está activo.
     * @return bool
     */
    public function isActiva(): bool
    {
        // Asegúrate de que el estado no sea null antes de comparar
        return $this->estado === 1;
    }

    /**
     * Verifica si el sprint está terminado.
     * @return bool
     */
    public function isTerminado(): bool
    {
        return $this->terminado === 1;
    }

    /**
     * Obtiene el sprint activo actual (estado=1 AND terminado=0).
     * Solo puede haber un sprint activo a la vez.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Sprint activo o null si no hay ninguno activo.
     * @throws Exception Si hay error en DB.
     */
    public static function getActiveSprint(PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM sprints
            WHERE
                estado = 1
                AND terminado = 0
            ORDER BY
                fecha_inicio DESC, id DESC
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado) {
                return self::construct($resultado);
            }

            return null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener sprint activo: " . $e->getMessage());
        }
    }

    /**
     * Verifica si todas las tareas asociadas a este sprint están terminadas.
     * Un sprint solo puede ser finalizado si todas sus tareas tienen id_estado = 3 (TERMINADO).
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si todas las tareas están terminadas, False si hay tareas pendientes.
     * @throws Exception Si hay error en DB.
     */
    public function canBeFinalized(PDO $conexion): bool
    {
        try {
            // Consulta para contar tareas asociadas al sprint que NO están terminadas
            $query = <<<SQL
            SELECT COUNT(*) as unfinished_count
            FROM tareas
            WHERE
                id_sprint = :sprint_id
                AND id_tarea_estado != :estado_terminado
                AND id_tarea_estado != :estado_eliminado
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':sprint_id', $this->getId(), PDO::PARAM_INT);
            $statement->bindValue(':estado_terminado', 3, PDO::PARAM_INT); // Tarea::ESTADO_TERMINADO
            $statement->bindValue(':estado_eliminado', 4, PDO::PARAM_INT); // Tarea::ESTADO_ELIMINADO
            $statement->execute();

            $resultado = $statement->fetch(PDO::FETCH_ASSOC);
            $unfinished_count = (int)$resultado['unfinished_count'];

            // El sprint puede ser finalizado solo si no hay tareas sin terminar
            return $unfinished_count === 0;

        } catch (PDOException $e) {
            throw new Exception("Error al verificar si el sprint puede ser finalizado: " . $e->getMessage());
        }
    }

    /**
     * Obtiene información detallada sobre las tareas pendientes de un sprint.
     * Útil para mostrar mensajes informativos al usuario sobre por qué no se puede finalizar.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array con información de tareas pendientes.
     * @throws Exception Si hay error en DB.
     */
    public function getUnfinishedTasksInfo(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                t.id,
                t.descripcion,
                te.descripcion as estado_descripcion
            FROM tareas t
            LEFT JOIN tareas_estados te ON t.id_tarea_estado = te.id
            WHERE
                t.id_sprint = :sprint_id
                AND t.id_tarea_estado != :estado_terminado
                AND t.id_tarea_estado != :estado_eliminado
            ORDER BY t.descripcion ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':sprint_id', $this->getId(), PDO::PARAM_INT);
            $statement->bindValue(':estado_terminado', 3, PDO::PARAM_INT); // Tarea::ESTADO_TERMINADO
            $statement->bindValue(':estado_eliminado', 4, PDO::PARAM_INT); // Tarea::ESTADO_ELIMINADO
            $statement->execute();

            return $statement->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            throw new Exception("Error al obtener información de tareas pendientes: " . $e->getMessage());
        }
    }

    /**
     * Obtiene sprints terminados que tienen tareas asociadas a un proyecto específico.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Sprint terminados.
     * @throws Exception Si hay error en DB.
     */
    public static function getFinishedSprintsByProject(int $id_proyecto, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT DISTINCT s.*
            FROM sprints s
            INNER JOIN tareas t ON s.id = t.id_sprint
            WHERE t.id_proyecto = :id_proyecto
                AND s.terminado = 1
                AND s.estado = 1
            ORDER BY s.fecha_inicio DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_proyecto', $id_proyecto, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener sprints terminados por proyecto: " . $e->getMessage());
        }
    }
}
