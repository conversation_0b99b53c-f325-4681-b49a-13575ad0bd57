<?php

// Use the Proyecto class with its namespace
use App\classes\Proyecto;
use App\classes\AliadoComercial; // Add AliadoComercial class
use App\classes\ProyectoCosto; // Add ProyectoCosto class
use App\classes\ClientePotencial; // Add ClientePotencial class

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}
/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included
// Include necessary files
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check if this is an AJAX request
$is_ajax = isset($_POST['is_ajax']) && $_POST['is_ajax'] === '1' || 
           (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest');

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en eproyecto.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	
	if ($is_ajax) {
		header('Content-Type: application/json');
		echo json_encode($response);
	} else {
		echo "Error: No se pudo conectar a la base de datos.";
	}
	exit;
}

// Variables to hold form input (useful if re-displaying form after error)
$descripcion  = '';
$fecha_inicio = '';    // Add variable for new field
$aliado       = null;  // Initialize aliado variable
$costos       = [];    // Initialize costs array
$totalCostosProyecto = 0.0; // Initialize total costs variable

// Check if we are editing an existing project
$proyectoId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$proyecto = null; // Initialize proyecto variable

if ($proyectoId > 0) {
	try {
		$proyecto = Proyecto::get($proyectoId, $conexion);
		if ($proyecto) {
			$descripcion  = $proyecto->getDescripcion() ?? '';
			$fecha_inicio = $proyecto->getFechaInicio() ?? '';  // Load fecha_inicio
			// $fecha_creacion = $proyecto->getFechaCreacion(); // Can still load if needed for display

			// Fetch associated AliadoComercial if ID exists
			$aliadoId = $proyecto->getIdAliadoComercial();
			if ($aliadoId !== null && $aliadoId > 0) {
				$aliado = AliadoComercial::get($aliadoId, $conexion); // Assuming AliadoComercial has a static get method
				// *** ADDED: Fetch and associate ClientePotencial ***
				if ($aliado) {
					$clientePotencial = ClientePotencial::getByAliadoId($aliado->getId(), $conexion);
					if ($clientePotencial) {
						$aliado->setClientePotencial($clientePotencial); // Assuming a setter exists
					}
				}

				// Fetch associated costs
				$costos = ProyectoCosto::getByProyectoId($proyectoId, $conexion);

				// Calculate the total sum of costs for this project
				$totalCostosProyecto = ProyectoCosto::getSumaCostosPorProyectoId($proyectoId, $conexion);
			}
		} else {
			$_SESSION['flash_message_error'] = "Proyecto no encontrado.";
			header('Location: lproyectos');
			exit;
		}
	} catch (Exception $e) {
		// Handle potential errors during data fetching
		error_log("Error fetching project or ally data in eproyecto.php: " . $e->getMessage());
		// Display a generic error or redirect
        $_SESSION['flash_message_error'] = "Proyecto no encontrado.";
        header('Location: lproyectos');
        exit;
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

	// Check if adding a cost or editing the project
	if (isset($_POST['action']) && $_POST['action'] === 'add_cost') {
		// --- Handle Adding a New Cost ---
		// Retrieve project ID specifically for this AJAX POST context
		$ajaxProyectoId = isset($_POST['id_proyecto_hidden']) ? (int)$_POST['id_proyecto_hidden'] : 0;
		try {
			if ($ajaxProyectoId <= 0) { // Use the ID received via POST
				throw new Exception("ID de proyecto no válido para añadir costo.");
			}

			// 1. Get cost data from $_POST
			$costoDescripcion = trim($_POST['descripcion_costo'] ?? '');
			$costoValor       = filter_input(INPUT_POST, 'valor_costo', FILTER_VALIDATE_FLOAT);  // Validate as float
			$costoFecha       = trim($_POST['fecha_costo'] ?? '');
			$costoNota        = trim($_POST['nota_adicional_costo'] ?? '');

			// 2. Validate cost data (ProyectoCosto class also validates)
			if (empty($costoDescripcion)) throw new InvalidArgumentException("La descripción del costo es requerida.");
			if ($costoValor === false || $costoValor < 0) throw new InvalidArgumentException("El valor del costo es requerido y debe ser un número positivo o cero.");
			if (empty($costoFecha)) throw new InvalidArgumentException("La fecha del costo es requerida.");

			// 3. Create and save the new ProyectoCosto
			$nuevoCosto = new ProyectoCosto();
			$nuevoCosto->setIdProyecto($ajaxProyectoId) // Use the ID received via POST
					   ->setDescripcion($costoDescripcion)
					   ->setValor((float)$costoValor) // Cast to float
					   ->setFecha($costoFecha)
					   ->setNotaAdicional($costoNota);

			if (!$nuevoCosto->guardar($conexion)) {
				throw new Exception("Hubo un error al guardar el nuevo costo.");
			}

			// --- Response ---
			if ($is_ajax) {
				// Prepare data for the new table row in JS
				$responseData = [
					'success' => true,
					'message' => 'Costo añadido exitosamente.',
					'costo' => [
						'id' => $nuevoCosto->getId(), // Include ID if needed for future actions
						'descripcion' => htmlspecialchars($nuevoCosto->getDescripcion() ?? ''),
						'valor_formatted' => '$' . number_format($nuevoCosto->getValor() ?? 0, 0, ',', '.'), // Pre-formatted value
						'fecha' => htmlspecialchars($nuevoCosto->getFecha() ?? ''),
						'nota_adicional' => nl2br(htmlspecialchars($nuevoCosto->getNotaAdicional() ?? '')),
					]
				];
				// --- Recalculate and add the new total cost to the response ---
				$responseData['totalCostosProyecto'] = ProyectoCosto::getSumaCostosPorProyectoId($ajaxProyectoId, $conexion);
				// --- End Recalculation ---
				header('Content-Type: application/json');
				echo json_encode($responseData);
				exit;
			} else {
				// Non-AJAX fallback (keep redirect)
				$_SESSION['flash_message_success_costo'] = "Costo añadido exitosamente.";
				header('Location: eproyecto.php?id=' . $ajaxProyectoId . '#project-costs-tab'); // Use correct ID and extension
				exit;
			}

		} catch (InvalidArgumentException $e) {
			$errorMessage = "Error de validación: " . $e->getMessage();
			$statusCode = 400; // Bad Request
		} catch (PDOException $e) {
			error_log("PDOException adding cost: " . $e->getMessage());
			$errorMessage = "Error de base de datos al añadir el costo.";
			$statusCode = 500; // Internal Server Error
		} catch (Exception $e) {
			error_log("Exception adding cost: " . $e->getMessage());
			$errorMessage = "Error al añadir el costo: " . $e->getMessage();
			$statusCode = 500; // Internal Server Error
		}

		// --- Error Response ---
		if ($is_ajax) {
			http_response_code($statusCode);
			header('Content-Type: application/json');
			echo json_encode(['success' => false, 'message' => $errorMessage]);
			exit;
		} else {
			// Non-AJAX fallback
			$_SESSION['flash_message_error_costo'] = $errorMessage;
			header('Location: eproyecto.php?id=' . $ajaxProyectoId . '#project-costs-tab'); // Use correct ID and extension
			exit;
		}

	} elseif (isset($_POST['action']) && $_POST['action'] === 'delete_cost') {
		// --- Handle Cost Deactivation (AJAX) ---
		$response = ['success' => false, 'message' => 'Error desconocido al desactivar costo.'];
		header('Content-Type: application/json'); // Ensure JSON header is set early

		// Basic check for AJAX request
		if (!$is_ajax) {
			 $response['message'] = 'Solicitud no válida.';
			 http_response_code(400); // Bad Request
			 echo json_encode($response);
			 exit;
		}

		// --- Validation ---
		$costoId = filter_input(INPUT_POST, 'costoId', FILTER_VALIDATE_INT);
		$proyectoId = filter_input(INPUT_POST, 'id_proyecto_hidden', FILTER_VALIDATE_INT); // Get project ID for context/logging

		if (!$costoId || $costoId <= 0) {
			$response['message'] = 'ID de costo inválido.';
			http_response_code(400);
			echo json_encode($response);
			exit;
		}
		if (!$proyectoId || $proyectoId <= 0) {
			// Although not strictly needed for desactivar, it's good practice to have project context
			$response['message'] = 'ID de proyecto inválido para contexto.';
			http_response_code(400);
			echo json_encode($response);
			exit;
		}

		// --- Attempt Deactivation ---
		try {
			// Use the desactivar method from ProyectoCosto
			$deactivated = ProyectoCosto::desactivar($costoId, $conexion);

			if ($deactivated) {
				$response['success'] = true;
				$response['message'] = 'Costo desactivado correctamente.';
				// --- Recalculate and add the new total cost to the response ---
				$response['totalCostosProyecto'] = ProyectoCosto::getSumaCostosPorProyectoId($proyectoId, $conexion);
			} else {
				// Deactivation might fail if ID doesn't exist or DB error occurs (handled by exception below)
				$response['message'] = 'No se pudo desactivar el costo. Es posible que no exista.';
			}
		} catch (Exception $e) { // Catches PDOException or InvalidArgumentException from desactivar
			error_log("Error deactivating cost ID {$costoId} for project {$proyectoId}: " . $e->getMessage());
			$response['message'] = 'Error del servidor al intentar desactivar el costo.';
			http_response_code(500); // Internal Server Error
		}

		// --- Send JSON Response ---
		echo json_encode($response);
		exit; // Stop script execution

	} else {
		// --- Handle Editing Project Details (Existing Logic) ---
		try {
			$descripcion = trim($_POST['descripcion'] ?? '');
			$fecha_inicio = trim($_POST['fecha_inicio'] ?? '');
			if (empty($descripcion)) throw new Exception("La descripción del proyecto es requerida.");
			if (empty($fecha_inicio)) throw new Exception("La fecha de inicio es requerida.");
			if ($proyectoId <= 0) throw new Exception("ID de proyecto no válido para editar.");

			$proyecto->setDescripcion($descripcion)->setFechaInicio($fecha_inicio);
			if ($proyecto->guardar($conexion)) {
			$_SESSION['flash_message_success'] = "Proyecto '$descripcion' actualizado exitosamente.";
			header('Location: lproyectos'); // Use RUTA constant for the base URL
			exit;
			} else {
			throw new Exception("Hubo un error al actualizar el proyecto. Intente nuevamente.");
			}
		} catch (PDOException $e) {
			error_log("PDOException updating project: " . $e->getMessage());
			$_SESSION['flash_message_error'] = 'Error de base de datos al actualizar el proyecto.';
		} catch (Exception $e) {
			error_log("Exception updating project: " . $e->getMessage());
			$_SESSION['flash_message_error'] = 'Ocurrió un error al actualizar el proyecto: ' . $e->getMessage();
		}
		// If update fails, redirect back to edit page to show error
		header('Location: eproyecto?id=' . $proyectoId);
		exit;
	}
}

// Only load the view for non-AJAX requests
if (!$is_ajax) {
    // Load the view file. Variables defined above will be available in the view.
    require_once __ROOT__ . '/views/admin/eproyecto.view.php';
}
?>
