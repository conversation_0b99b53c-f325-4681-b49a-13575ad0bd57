<?php

// Use the AliadoComercial class with its namespace
use App\classes\AliadoComercial;
use App\classes\ClientePotencial;
use App\classes\Proyecto; // Add Proyecto class

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

// --- Include necessary files ---
// Adjust paths based on the actual location of caliado_comercial.php relative to the project root
require_once dirname(__DIR__, 2) . '/config/config.php';        // Defines __ROOT__, constants etc.
require_once __ROOT__ . '/src/sessions/sessions.php';           // For session handling & constants
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';        // For validation functions if needed here
require_once __ROOT__ . '/src/general/preparar.php';            // <PERSON><PERSON> login check, feedback var init, PDO connection ($conexion)
// --- End Include files ---

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
/** @var AliadoComercial|null $aliado */
$aliado = null;                                             // Initialize as null for the specific aliado
$proyecto_asociado = null;                                  // Initialize associated project as null
// Feedback variables ($success_text, $error_text etc.) are initialized in preparar.php
// This controller primarily SETS flash messages on error/redirect. The VIEW will display them.
#endregion init variables

#region region Handle Request Method & Get Aliado ID
// This page should only respond to POST requests containing the ID
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
	// If accessed directly via GET or another method, redirect to the list
	$_SESSION['flash_message_error'] = "Acceso inválido a la página de perfil.";
	header('Location: laliados_comerciales'); // Use the URL defined in .htaccess or your routing
	exit;
}

$aliado_id = null;
if (isset($_POST['id']) && filter_var($_POST['id'], FILTER_VALIDATE_INT, ['options' => ['min_range' => 1]])) {
	$aliado_id = (int)$_POST['id'];
} else {
	// Handle error: No valid ID provided via POST
	$_SESSION['flash_message_error'] = "ID de aliado no proporcionado o inválido para ver el perfil.";
	header('Location: laliados_comerciales'); // Redirect back to the list
	exit;
}
#endregion Handle Request Method & Get Aliado ID

#region region Fetch Aliado Data
try {
	// Use the static method from the AliadoComercial class to find the aliado by ID
	// Ensure get method accepts and uses the PDO connection
	$aliado = AliadoComercial::get($aliado_id, $conexion);

	if (!$aliado) {
		// Handle error: Aliado not found in the database
		$_SESSION['flash_message_error'] = "Aliado con ID " . htmlspecialchars($aliado_id, ENT_QUOTES, 'UTF-8') . " no encontrado.";
		header('Location: laliados_comerciales'); // Redirect back to the list
		exit;
	}

	// Add checks here if the fetched aliado is active or meets other criteria if necessary
	if ($aliado->getEstado() == 0) {
		$_SESSION['flash_message_error'] = "El aliado con ID " . htmlspecialchars($aliado_id, ENT_QUOTES, 'UTF-8') . " no está activo.";
		header('Location: laliados_comerciales');
		exit;
	}

	//Asogiar el resto de informacion si no hubieron errores con la clase AliadoComercial
	$aliado->setClientePotencial(ClientePotencial::getByAliadoId($aliado_id, $conexion));

	// If the ally is viable, fetch the associated active project (if any)
	if ($aliado->getViable() === 1) {
		$proyecto_asociado = Proyecto::getByAliadoComercialId($aliado->getId(), $conexion);
	}

} catch (PDOException $e) {
	// Specific handling for database errors during fetch
	error_log("Database error fetching aliado ID {$aliado_id}: " . $e->getMessage()); // Log detailed error
	$_SESSION['flash_message_error'] = "Error de base de datos al obtener la información del aliado.";
	header('Location: laliados_comerciales'); // Redirect back to the list
	exit;
} catch (Exception $e) {
	// General error handling during fetch
	error_log("Error fetching aliado ID {$aliado_id}: " . $e->getMessage()); // Log detailed error
	$_SESSION['flash_message_error'] = "Ocurrió un error inesperado al obtener la información del aliado.";
	header('Location: laliados_comerciales'); // Redirect back to the list
	exit;
}
#endregion Fetch Aliado Data

// If execution reaches here, the $aliado object is valid and populated.
// Load the view file. The $aliado object will be available in the view.
// Ensure the path to the view file is correct.
require_once __ROOT__ . '/views/admin/caliado_comercial.view.php';

?>
