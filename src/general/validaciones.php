<?php

/**
 * @throws Exception
 */
function validar_campovacio($campo, $error_txt): void
{
    if (empty($campo)) {
        throw new Exception($error_txt);
    }
}

function validate_emptyfieldexcludezero($campo, $error_txt) {
    if (empty($campo) && $campo != 0) {
        throw new Exception($error_txt);
    }
}

function validar_textovacionotzero($campo, $error_txt) {
    if (empty($campo) && $campo != 0) {
        throw new Exception($error_txt);
    }
}

function validate_letternumberonly($text,$error_txt){
    // solo permitir caracteres alfanumericos.
	if(!ctype_alnum($text)){
		throw new Exception($error_txt);
	}
}

function validate_numberonly($text,$error_txt){
    // solo permitir caracteres alfanumericos.
    if(!ctype_digit($text)){
        throw new Exception($error_txt);
    }
}

?>