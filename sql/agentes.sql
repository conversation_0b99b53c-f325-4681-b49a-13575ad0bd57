-- Create agentes table
CREATE TABLE IF NOT EXISTS agentes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    descripcion VARCHAR(255) NOT NULL,
    estado TINYINT(1) DEFAULT 1 NOT NULL,
    mensualidad DOUBLE DEFAULT 0 NOT NULL,
    n_mensajes INT(3) DEFAULT 0 NOT NULL,
    balance DOUBLE DEFAULT 0 NOT NULL,
    costo_por_mensaje DOUBLE DEFAULT 0 NOT NULL,
    costo_total DOUBLE DEFAULT 0 NOT NULL
) COLLATE=utf8mb4_unicode_ci;

-- Create index for better performance
CREATE INDEX idx_estado ON agentes(estado);

-- Insert sample agents
INSERT INTO agentes (descripcion, estado) VALUES 
('GPT-4', 1),
('Claude 3.5 Sonnet', 1),
('Gemini Pro', 1),
('GPT-3.5 Turbo', 1),
('<PERSON> 3 Haiku', 1),
('<PERSON><PERSON><PERSON> 2', 1),
('Mistral 7B', 1),
('Pa<PERSON> 2', 1),
('Cohere Command', 1),
('Anthropic Claude', 1);
