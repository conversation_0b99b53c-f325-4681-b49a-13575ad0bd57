-- Create proyectos_modulos table
CREATE TABLE IF NOT EXISTS proyectos_modulos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_proyecto INT NOT NULL,
    descripcion TEXT NOT NULL,
    estado TINYINT(1) DEFAULT 1 NOT NULL,
    FOREIGN KEY (id_proyecto) REFERENCES proyectos(id) ON UPDATE CASCADE ON DELETE CASCADE
) COLLATE=utf8mb4_unicode_ci;

-- Create index for better performance
CREATE INDEX idx_id_proyecto ON proyectos_modulos(id_proyecto);
CREATE INDEX idx_estado ON proyectos_modulos(estado);
