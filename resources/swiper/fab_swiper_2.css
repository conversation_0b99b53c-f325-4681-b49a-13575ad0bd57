.swiper {
    width: 100%;
    height: auto; /* Auto height to accommodate multi-line text */
    min-height: 60px; /* Minimum height to ensure visibility */
    overflow: hidden;
    display: flex; /* Use flexbox for better alignment */
    align-items: center; /* Center items vertically */
}

.swiper-wrapper {
    align-items: center; /* Ensure wrapper aligns items in the center */
    height: 100%; /* Full height of container */
}

.swiper-slide {
    width: 500px;
    display: flex;
    justify-content: center;
    align-items: center; /* Center content vertically */
    font-size: 16px;
    color: white;
    text-align: center;
    padding: 15px 40px 15px 10px; /* Increased padding for better spacing */
    position: relative; /* Ensure proper positioning context */
    line-height: 1.4; /* Better line spacing for wrapped text */
    white-space: normal; /* Allow text to wrap */
    min-height: 40px; /* Ensure minimum height even with short text */
    box-sizing: border-box; /* Include padding in height calculation */
}
.swiper-slide:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 15px; /* Position dot further to the right */
    top: 50%; /* Position at middle of container */
    transform: translateY(-50%); /* Perfect vertical centering */
    width: 8px; /* Consistent dot size */
    height: 8px; /* Consistent dot size */
    background: white; /* Dot color */
    border-radius: 50%; /* Make it circular */
    z-index: 2; /* Ensure dots appear above text */
    margin: 0; /* Remove any margin that might affect positioning */
    padding: 0; /* Remove any padding that might affect positioning */
    display: block; /* Ensure consistent display */
}

/* Add responsive styles for mobile devices */
@media (max-width: 768px) {
    .swiper {
        min-height: 80px; /* Increased minimum height for mobile */
    }

    .swiper-wrapper {
        align-items: center; /* Maintain vertical centering on mobile */
    }

    .swiper-slide {
        width: 300px; /* Smaller width on mobile */
        padding: 12px 35px 12px 8px; /* Adjusted padding for mobile */
        font-size: 14px; /* Slightly smaller font on mobile */
        min-height: 50px; /* Ensure consistent height on mobile */
    }

    .swiper-slide:not(:last-child)::after {
        right: 12px; /* Adjust dot position for mobile */
        width: 6px; /* Smaller dot size on mobile */
        height: 6px; /* Smaller dot size on mobile */
    }
}