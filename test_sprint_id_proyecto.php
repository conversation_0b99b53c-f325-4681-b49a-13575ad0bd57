<?php

require_once 'config/config.php';

use App\classes\Sprint;
use App\classes\Proyecto;

try {
    // Test creating a new Sprint object
    $sprint = new Sprint();
    echo "✓ Sprint object created successfully\n";
    
    // Test setting and getting id_proyecto
    $sprint->setIdProyecto(123);
    $id_proyecto = $sprint->getIdProyecto();
    echo "✓ Set id_proyecto to 123, got: $id_proyecto\n";
    
    // Test setting null value
    $sprint->setIdProyecto(null);
    $id_proyecto_null = $sprint->getIdProyecto();
    echo "✓ Set id_proyecto to null, got: " . ($id_proyecto_null === null ? 'null' : $id_proyecto_null) . "\n";
    
    // Test construct method with array data
    $data = [
        'id' => 1,
        'id_proyecto' => 456,
        'descripcion' => 'Test Sprint',
        'fecha_inicio' => '2024-01-01',
        'fecha_fin' => '2024-01-31',
        'terminado' => 0,
        'cambios_bd' => null,
        'cambios_resources' => null,
        'estado' => 1
    ];
    
    $sprint_from_array = Sprint::construct($data);
    echo "✓ Sprint constructed from array, id_proyecto: " . $sprint_from_array->getIdProyecto() . "\n";
    
    // Test construct method with null id_proyecto
    $data_null = $data;
    $data_null['id_proyecto'] = null;
    $sprint_null = Sprint::construct($data_null);
    echo "✓ Sprint constructed with null id_proyecto: " . ($sprint_null->getIdProyecto() === null ? 'null' : $sprint_null->getIdProyecto()) . "\n";
    
    echo "\n✅ All tests passed! The id_proyecto field has been successfully added to the Sprint class.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}