<?php

declare(strict_types=1);

namespace App\classes;
// Asegúrate que el namespace sea correcto

// Importar clases necesarias
use App\classes\Freelancer;
use Exception;
use PDO;
use PDOException;

/**
 * Represents the document information (CV, certifications, portfolio) for a freelancer.
 * Assumes properties store paths/filenames or URLs.
 */
class FreelancerDocumento
{
	// --- Attributes ---
	private ?int        $id              = null;
	private ?Freelancer $freelancer      = null; // Objeto Freelancer asociado
	private ?string     $curriculum      = null; // Path/filename or URL
	private ?string     $certificaciones = null; // Path/filename or URL
	const URL_CARPETA = __ROOT__ . '/resources/uploads/freelancers/';
	
	/**
	 * Constructor: Initializes properties.
	 */
	public function __construct()
	{
		$this->id              = null;
		$this->freelancer      = new Freelancer(); // Inicializar con objeto Freelancer vacío
		$this->curriculum      = null;
		$this->certificaciones = null;
	}
	
	/**
	 * Static factory method to create an instance from an array (e.g., DB result).
	 * Creates associated Freelancer object and assigns its ID.
	 *
	 * @param array $resultado Associative array.
	 *
	 * @return self Instance of FreelancerDocumento.
	 * @throws Exception If there's an error.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                  = new self();
			$objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->curriculum      = $resultado['curriculum'] ?? null;
			$objeto->certificaciones = $resultado['certificaciones'] ?? null;
			
			// Assign ID to the existing Freelancer object
			$freelancerId = isset($resultado['id_freelancer']) ? (int)$resultado['id_freelancer'] : null;
			if ($freelancerId !== null && $objeto->freelancer) {
				$objeto->freelancer->setId($freelancerId); // Assumes setId() exists in Freelancer
			}
			
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error constructing FreelancerDocumento: " . $e->getMessage());
		}
	}
	
	// --- Data Access Methods ---
	
	/**
	 * Gets a document record set by its own primary key ID.
	 * Loads the related Freelancer object.
	 *
	 * @param int $id       The ID of the document record set.
	 * @param PDO $conexion PDO connection.
	 *
	 * @return self|null FreelancerDocumento object or null if not found.
	 * @throws Exception On DB error.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query     = <<<SQL
            SELECT * FROM freelancer_documentos WHERE id = :id LIMIT 1
            SQL;
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			if ($resultado) {
				$documentoSet = self::construct($resultado); // Base object with Freelancer ID set
				
				// Load full related Freelancer object
				$id_freelancer = $documentoSet->getFreelancer()?->getId();
				if ($id_freelancer) {
					$documentoSet->setFreelancer(Freelancer::get($id_freelancer, $conexion));
				}
				return $documentoSet;
			} else {
				return null;
			}
		} catch (Exception $e) {
			throw new Exception("Error getting FreelancerDocumento (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Gets the document record set for a specific Freelancer ID.
	 * Loads the full Freelancer object.
	 *
	 * @param int $id_freelancer Freelancer ID.
	 * @param PDO $conexion      PDO connection.
	 *
	 * @return self|null The FreelancerDocumento object or null if not found.
	 * @throws Exception On DB error.
	 */
	public static function getByFreelancerId(int $id_freelancer, PDO $conexion): ?self
	{
		try {
			// Pre-load the Freelancer object
			$freelancer = Freelancer::get($id_freelancer, $conexion);
			if (!$freelancer) {
				return null; // Or throw exception if freelancer must exist
			}
			
			$query     = <<<SQL
            SELECT * FROM freelancer_documentos
            WHERE id_freelancer = :id_freelancer
            LIMIT 1 -- Assumes one record per freelancer due to suggested UNIQUE constraint
            SQL;
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_freelancer", $id_freelancer, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			if ($resultado) {
				$documentoSet = self::construct($resultado); // Creates object with IDs
				$documentoSet->setFreelancer($freelancer);   // Set the pre-loaded Freelancer
				return $documentoSet;
			} else {
				return null; // No document record found for this freelancer
			}
		} catch (Exception $e) {
			throw new Exception("Error getting documents for Freelancer ID $id_freelancer: " . $e->getMessage());
		}
	}
	
	/**
	 * Guarda (inserta o actualiza) el registro de documentos para el freelancer asociado.
	 * Determina si insertar o actualizar basándose en si ya existe un registro para el id_freelancer.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True en caso de éxito, False en caso contrario.
	 * @throws Exception Si el objeto Freelancer asociado o su ID faltan, o en error de DB.
	 */
	public function guardar(PDO $conexion): bool
	{
		$idFreelancer = $this->getFreelancer()?->getId();
		if ($idFreelancer === null || $idFreelancer <= 0) {
			throw new Exception("Valid Freelancer object with ID is required to save documents.");
		}
		
		// Verifica si ya existe un registro para este freelancer_id
		$existingId = $this->_findIdByFreelancerId($idFreelancer, $conexion);
		
		if ($existingId !== null) {
			// Si existe, actualiza ese registro
			$this->setId($existingId); // Asegurarse que el objeto tenga el ID correcto
			return $this->_update($conexion);
		} else {
			// Si no existe, inserta uno nuevo
			$newId = $this->_insert($conexion);
			if ($newId !== false) {
				$this->setId($newId);
				return true;
			}
			return false;
		}
	}
	
	/**
	 * Busca el ID del registro de documentos existente para un freelancer_id. (Método auxiliar)
	 *
	 * @param int $id_freelancer
	 * @param PDO $conexion
	 *
	 * @return int|null
	 * @throws Exception
	 */
	private function _findIdByFreelancerId(int $id_freelancer, PDO $conexion): ?int
	{
		try {
			$query = "SELECT id FROM freelancer_documentos WHERE id_freelancer = :id_freelancer LIMIT 1";
			$stmt  = $conexion->prepare($query);
			$stmt->bindValue(':id_freelancer', $id_freelancer, PDO::PARAM_INT);
			$stmt->execute();
			$result = $stmt->fetchColumn();
			return ($result !== false) ? (int)$result : null;
		} catch (PDOException $e) {
			throw new Exception("Error checking for existing documents for Freelancer ID $id_freelancer: " . $e->getMessage());
		}
	}
	
	
	/**
	 * Inserta un nuevo registro de documentos. (Método privado)
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return int|false New ID or false on error.
	 * @throws Exception On DB error or missing Freelancer ID.
	 */
	private function _insert(PDO $conexion): int|false
	{
		$idFreelancer = $this->getFreelancer()?->getId();
		if ($idFreelancer === null) {
			throw new Exception("Cannot insert documents: Freelancer ID is missing.");
		}
		
		try {
			$query = <<<SQL
            INSERT INTO freelancer_documentos (
                id_freelancer, curriculum, certificaciones
            ) VALUES (
                :id_freelancer, :curriculum, :certificaciones
            )
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_freelancer', $idFreelancer, PDO::PARAM_INT);
			$statement->bindValue(':curriculum', $this->getCurriculum(), PDO::PARAM_STR);           // PDO maneja NULL
			$statement->bindValue(':certificaciones', $this->getCertificaciones(), PDO::PARAM_STR); // PDO maneja NULL
			
			$success = $statement->execute();
			
			return $success ? (int)$conexion->lastInsertId() : false;
			
		} catch (PDOException $e) {
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Error de UK en id_freelancer
				throw new Exception("Error creating document record: A record already exists for this Freelancer (ID: $idFreelancer).");
			} else {
				throw new Exception("Database error creating FreelancerDocumento: " . $e->getMessage(), (int)$e->getCode(), $e);
			}
		}
	}
	
	/**
	 * Actualiza un registro de documentos existente. (Método privado)
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return bool True on success, False on failure.
	 * @throws Exception On DB error or missing IDs.
	 */
	private function _update(PDO $conexion): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("Valid Document Record ID is required to update.");
		}
		$idFreelancer = $this->getFreelancer()?->getId();
		if ($idFreelancer === null) {
			throw new Exception("Cannot update documents: Freelancer ID is missing.");
		}
		
		try {
			$query = <<<SQL
            UPDATE freelancer_documentos SET
                curriculum = :curriculum,
                certificaciones = :certificaciones
            WHERE id = :id
            -- WHERE id_freelancer = :id_freelancer -- Alternativa
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':curriculum', $this->getCurriculum(), PDO::PARAM_STR);
			$statement->bindValue(':certificaciones', $this->getCertificaciones(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
			// $statement->bindValue(':id_freelancer', $idFreelancer, PDO::PARAM_INT); // Si usas WHERE id_freelancer
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			throw new Exception("Database error updating FreelancerDocumento (ID: {$this->getId()}): " . $e->getMessage(), (int)$e->getCode(), $e);
		}
	}
	
	/**
	 * Deletes this specific document record set from the database.
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return bool True on success, False on failure.
	 * @throws Exception If ID is missing or on DB error.
	 */
	public function delete(PDO $conexion): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("Valid ID is required to delete the document set.");
		}
		try {
			$query     = "DELETE FROM freelancer_documentos WHERE id = :id";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
			return $statement->execute();
		} catch (PDOException $e) {
			throw new Exception("Database error deleting FreelancerDocumento (ID: {$this->getId()}): " . $e->getMessage());
		}
	}
	
	/**
	 * Deletes the document record set for a given Freelancer ID.
	 *
	 * @param int $id_freelancer Freelancer ID.
	 * @param PDO $conexion      PDO connection.
	 *
	 * @return bool True on success, False on failure or if not found.
	 * @throws Exception On DB error.
	 */
	public static function deleteByFreelancerId(int $id_freelancer, PDO $conexion): bool
	{
		if ($id_freelancer <= 0) {
			throw new Exception("Valid Freelancer ID is required to delete documents.");
		}
		try {
			$query     = "DELETE FROM freelancer_documentos WHERE id_freelancer = :id_freelancer";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_freelancer', $id_freelancer, PDO::PARAM_INT);
			return $statement->execute();
		} catch (PDOException $e) {
			throw new Exception("Database error deleting documents for Freelancer ID $id_freelancer: " . $e->getMessage());
		}
	}
	
	
	// --- Getters and Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getFreelancer(): ?Freelancer
	{
		return $this->freelancer;
	}
	
	public function setFreelancer(?Freelancer $freelancer): self
	{
		$this->freelancer = $freelancer;
		return $this;
	}
	
	public function getCurriculum(): ?string
	{
		return $this->curriculum;
	}
	
	public function setCurriculum(?string $curriculum): self
	{
		$this->curriculum = $curriculum;
		return $this;
	}
	
	public function getCertificaciones(): ?string
	{
		return $this->certificaciones;
	}
	
	public function setCertificaciones(?string $certificaciones): self
	{
		$this->certificaciones = $certificaciones;
		return $this;
	}
	
} // End class FreelancerDocumento
